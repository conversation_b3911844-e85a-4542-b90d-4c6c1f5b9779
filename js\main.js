/**
 * 个人主页JavaScript交互功能
 * 实现现代化的用户交互体验
 */

// DOM元素选择器
const elements = {
    navbar: document.getElementById('navbar'),
    navMenu: document.getElementById('nav-menu'),
    hamburger: document.getElementById('hamburger'),
    themeToggle: document.getElementById('theme-toggle'),
    backToTop: document.getElementById('back-to-top'),
    contactForm: document.getElementById('contact-form'),
    navLinks: document.querySelectorAll('.nav-link'),
    skillProgress: document.querySelectorAll('.skill-progress'),
    fadeElements: document.querySelectorAll('.fade-in')
};

// 应用程序状态
const state = {
    currentTheme: localStorage.getItem('theme') || 'light',
    isMenuOpen: false,
    lastScrollY: 0
};

/**
 * 初始化应用程序
 */
function init() {
    // 设置初始主题
    setTheme(state.currentTheme);
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 初始化滚动相关功能
    handleScroll();
    
    // 初始化动画
    initAnimations();
    
    // 设置技能进度条动画
    initSkillBars();
    
    console.log('个人主页已初始化完成');
}

/**
 * 绑定所有事件监听器
 */
function bindEventListeners() {
    // 滚动事件
    window.addEventListener('scroll', throttle(handleScroll, 16));
    
    // 导航菜单切换
    if (elements.hamburger) {
        elements.hamburger.addEventListener('click', toggleMobileMenu);
    }
    
    // 主题切换
    if (elements.themeToggle) {
        elements.themeToggle.addEventListener('click', toggleTheme);
    }
    
    // 返回顶部按钮
    if (elements.backToTop) {
        elements.backToTop.addEventListener('click', scrollToTop);
    }
    
    // 导航链接平滑滚动
    elements.navLinks.forEach(link => {
        link.addEventListener('click', handleNavClick);
    });
    
    // 联系表单提交
    if (elements.contactForm) {
        elements.contactForm.addEventListener('submit', handleFormSubmit);
    }
    
    // 窗口大小改变
    window.addEventListener('resize', handleResize);
    
    // 键盘导航
    document.addEventListener('keydown', handleKeydown);
}

/**
 * 处理滚动事件
 */
function handleScroll() {
    const scrollY = window.scrollY;
    
    // 导航栏样式变化
    if (elements.navbar) {
        if (scrollY > 100) {
            elements.navbar.classList.add('scrolled');
        } else {
            elements.navbar.classList.remove('scrolled');
        }
    }
    
    // 返回顶部按钮显示/隐藏
    if (elements.backToTop) {
        if (scrollY > 500) {
            elements.backToTop.classList.add('visible');
        } else {
            elements.backToTop.classList.remove('visible');
        }
    }
    
    // 更新活动导航链接
    updateActiveNavLink();
    
    // 触发滚动动画
    triggerScrollAnimations();
    
    state.lastScrollY = scrollY;
}

/**
 * 切换移动端菜单
 */
function toggleMobileMenu() {
    state.isMenuOpen = !state.isMenuOpen;
    
    if (elements.hamburger && elements.navMenu) {
        elements.hamburger.classList.toggle('active', state.isMenuOpen);
        elements.navMenu.classList.toggle('active', state.isMenuOpen);
        
        // 防止背景滚动
        document.body.style.overflow = state.isMenuOpen ? 'hidden' : '';
    }
}

/**
 * 切换主题
 */
function toggleTheme() {
    const newTheme = state.currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

/**
 * 设置主题
 */
function setTheme(theme) {
    state.currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // 更新主题切换按钮图标
    if (elements.themeToggle) {
        const icon = elements.themeToggle.querySelector('i');
        if (icon) {
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }
}

/**
 * 返回顶部
 */
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

/**
 * 处理导航链接点击
 */
function handleNavClick(event) {
    event.preventDefault();
    
    const href = event.target.getAttribute('href');
    if (href && href.startsWith('#')) {
        const targetElement = document.querySelector(href);
        if (targetElement) {
            const offsetTop = targetElement.offsetTop - 80; // 考虑导航栏高度
            
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
            
            // 关闭移动端菜单
            if (state.isMenuOpen) {
                toggleMobileMenu();
            }
        }
    }
}

/**
 * 更新活动导航链接
 */
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const scrollPos = window.scrollY + 100;
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');
        
        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
            // 移除所有活动状态
            elements.navLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 添加当前活动状态
            const activeLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }
    });
}

/**
 * 处理表单提交
 */
function handleFormSubmit(event) {
    event.preventDefault();
    
    // 获取表单数据
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData);
    
    // 简单的表单验证
    if (!data.name || !data.email || !data.message) {
        showNotification('请填写所有必填字段', 'error');
        return;
    }
    
    if (!isValidEmail(data.email)) {
        showNotification('请输入有效的邮箱地址', 'error');
        return;
    }
    
    // 模拟表单提交
    showNotification('消息发送成功！我会尽快回复您。', 'success');
    event.target.reset();
}

/**
 * 邮箱验证
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 添加样式
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '16px 24px',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease-in-out',
        backgroundColor: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'
    });
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

/**
 * 初始化技能进度条动画
 */
function initSkillBars() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const progress = progressBar.getAttribute('data-progress');
                
                setTimeout(() => {
                    progressBar.style.width = progress + '%';
                }, 200);
                
                observer.unobserve(progressBar);
            }
        });
    }, { threshold: 0.5 });
    
    elements.skillProgress.forEach(bar => {
        observer.observe(bar);
    });
}

/**
 * 初始化滚动动画
 */
function initAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, { threshold: 0.1 });
    
    // 为所有需要动画的元素添加观察
    const animatedElements = document.querySelectorAll('.fade-in, .skill-item, .project-card, .feature-card');
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

/**
 * 触发滚动动画
 */
function triggerScrollAnimations() {
    const animatedElements = document.querySelectorAll('.fade-in:not(.visible)');
    
    animatedElements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        
        if (elementTop < window.innerHeight - elementVisible) {
            element.classList.add('visible');
        }
    });
}

/**
 * 处理窗口大小改变
 */
function handleResize() {
    // 关闭移动端菜单
    if (window.innerWidth > 1024 && state.isMenuOpen) {
        toggleMobileMenu();
    }
}

/**
 * 处理键盘导航
 */
function handleKeydown(event) {
    // ESC键关闭移动端菜单
    if (event.key === 'Escape' && state.isMenuOpen) {
        toggleMobileMenu();
    }
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);

// 导出功能供其他脚本使用
window.PersonalSite = {
    setTheme,
    showNotification,
    scrollToTop
};
