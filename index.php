<?php
/**
 * 个人主页 - 现代化设计
 * 采用2025年流行的UI设计风格
 * 支持深色模式和响应式设计
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="个人主页 - 展示技能、项目和联系方式">
    <meta name="keywords" content="个人主页,作品集,技能展示,项目展示">
    <title>张三 - 全栈开发工程师</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="preload" href="js/main.js" as="script">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <!-- 网站图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💻</text></svg>">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-code"></i>
                <span>张三</span>
            </div>
            
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">首页</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">关于我</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">技能</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">项目</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">联系</a>
                </li>
            </ul>
            
            <div class="nav-controls">
                <!-- 深色模式切换按钮 -->
                <button class="theme-toggle" id="theme-toggle" aria-label="切换深色模式">
                    <i class="fas fa-moon"></i>
                </button>
                
                <!-- 移动端菜单按钮 -->
                <button class="hamburger" id="hamburger" aria-label="打开菜单">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- 首页/英雄区域 -->
        <section id="home" class="hero">
            <div class="hero-background">
                <div class="hero-gradient"></div>
                <div class="hero-particles"></div>
            </div>
            
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="greeting">你好，我是</span>
                        <span class="name">张三</span>
                    </h1>
                    <p class="hero-subtitle">全栈开发工程师 & 技术爱好者</p>
                    <p class="hero-description">
                        专注于现代Web开发技术，致力于创造优雅高效的数字体验。
                        拥有丰富的前端和后端开发经验，热爱学习新技术。
                    </p>
                    
                    <div class="hero-buttons">
                        <a href="#projects" class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            查看项目
                        </a>
                        <a href="#contact" class="btn btn-secondary">
                            <i class="fas fa-envelope"></i>
                            联系我
                        </a>
                    </div>
                </div>
                
                <div class="hero-image">
                    <div class="avatar-container">
                        <div class="avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="avatar-ring"></div>
                    </div>
                </div>
            </div>
            
            <!-- 滚动提示 -->
            <div class="scroll-indicator">
                <i class="fas fa-chevron-down"></i>
            </div>
        </section>

        <!-- 关于我 -->
        <section id="about" class="about">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">关于我</h2>
                    <p class="section-subtitle">了解我的背景和经历</p>
                </div>
                
                <div class="about-content">
                    <div class="about-text">
                        <div class="about-card">
                            <h3>个人简介</h3>
                            <p>
                                我是一名充满热情的全栈开发工程师，拥有5年的Web开发经验。
                                专注于使用现代技术栈构建高质量的Web应用程序。
                                我相信代码不仅仅是功能的实现，更是艺术的表达。
                            </p>
                        </div>
                        
                        <div class="about-stats">
                            <div class="stat-item">
                                <div class="stat-number">5+</div>
                                <div class="stat-label">年经验</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">50+</div>
                                <div class="stat-label">完成项目</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">100%</div>
                                <div class="stat-label">客户满意度</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="about-features">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h4>创新思维</h4>
                            <p>始终保持对新技术的好奇心，善于将创新想法转化为实际解决方案。</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h4>团队协作</h4>
                            <p>具备优秀的沟通能力和团队合作精神，能够高效协作完成项目。</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h4>技术专精</h4>
                            <p>深入掌握多种编程语言和框架，持续学习最新的开发技术。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 技能展示 -->
        <section id="skills" class="skills">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">技能专长</h2>
                    <p class="section-subtitle">我掌握的技术栈和工具</p>
                </div>
                
                <div class="skills-content">
                    <div class="skill-category">
                        <h3>前端开发</h3>
                        <div class="skill-grid">
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fab fa-html5"></i>
                                </div>
                                <div class="skill-info">
                                    <span class="skill-name">HTML5</span>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-progress="95"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fab fa-css3-alt"></i>
                                </div>
                                <div class="skill-info">
                                    <span class="skill-name">CSS3</span>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-progress="90"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fab fa-js-square"></i>
                                </div>
                                <div class="skill-info">
                                    <span class="skill-name">JavaScript</span>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-progress="88"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fab fa-react"></i>
                                </div>
                                <div class="skill-info">
                                    <span class="skill-name">React</span>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-progress="85"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h3>后端开发</h3>
                        <div class="skill-grid">
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fab fa-php"></i>
                                </div>
                                <div class="skill-info">
                                    <span class="skill-name">PHP</span>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-progress="92"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fab fa-node-js"></i>
                                </div>
                                <div class="skill-info">
                                    <span class="skill-name">Node.js</span>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-progress="80"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="skill-info">
                                    <span class="skill-name">MySQL</span>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-progress="85"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fab fa-python"></i>
                                </div>
                                <div class="skill-info">
                                    <span class="skill-name">Python</span>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-progress="75"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h3>工具与其他</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">Git</span>
                            <span class="skill-tag">Docker</span>
                            <span class="skill-tag">AWS</span>
                            <span class="skill-tag">Linux</span>
                            <span class="skill-tag">Webpack</span>
                            <span class="skill-tag">Sass</span>
                            <span class="skill-tag">TypeScript</span>
                            <span class="skill-tag">Vue.js</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 项目展示 -->
        <section id="projects" class="projects">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">项目作品</h2>
                    <p class="section-subtitle">我参与开发的精选项目</p>
                </div>

                <div class="projects-grid">
                    <div class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="project-overlay">
                                <div class="project-links">
                                    <a href="#" class="project-link" aria-label="查看演示">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="#" class="project-link" aria-label="查看代码">
                                        <i class="fab fa-github"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">电商平台</h3>
                            <p class="project-description">
                                基于React和Node.js开发的现代化电商平台，
                                支持商品管理、订单处理、支付集成等完整功能。
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">React</span>
                                <span class="tech-tag">Node.js</span>
                                <span class="tech-tag">MongoDB</span>
                                <span class="tech-tag">Express</span>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="project-overlay">
                                <div class="project-links">
                                    <a href="#" class="project-link" aria-label="查看演示">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="#" class="project-link" aria-label="查看代码">
                                        <i class="fab fa-github"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">数据可视化平台</h3>
                            <p class="project-description">
                                企业级数据分析和可视化平台，提供实时数据监控、
                                图表生成和报告导出功能。
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Vue.js</span>
                                <span class="tech-tag">D3.js</span>
                                <span class="tech-tag">PHP</span>
                                <span class="tech-tag">MySQL</span>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="project-overlay">
                                <div class="project-links">
                                    <a href="#" class="project-link" aria-label="查看演示">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="#" class="project-link" aria-label="查看代码">
                                        <i class="fab fa-github"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">移动端应用</h3>
                            <p class="project-description">
                                跨平台移动应用，提供用户管理、内容分享、
                                社交互动等功能，支持iOS和Android。
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">React Native</span>
                                <span class="tech-tag">Firebase</span>
                                <span class="tech-tag">Redux</span>
                                <span class="tech-tag">TypeScript</span>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">
                                <i class="fas fa-blog"></i>
                            </div>
                            <div class="project-overlay">
                                <div class="project-links">
                                    <a href="#" class="project-link" aria-label="查看演示">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="#" class="project-link" aria-label="查看代码">
                                        <i class="fab fa-github"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">个人博客系统</h3>
                            <p class="project-description">
                                现代化的博客管理系统，支持Markdown编辑、
                                标签分类、评论系统和SEO优化。
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Next.js</span>
                                <span class="tech-tag">Tailwind CSS</span>
                                <span class="tech-tag">Prisma</span>
                                <span class="tech-tag">PostgreSQL</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系方式 -->
        <section id="contact" class="contact">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">联系我</h2>
                    <p class="section-subtitle">让我们一起创造精彩的项目</p>
                </div>

                <div class="contact-content">
                    <div class="contact-info">
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h4>邮箱</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-details">
                                <h4>电话</h4>
                                <p>+86 138 0000 0000</p>
                            </div>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-details">
                                <h4>位置</h4>
                                <p>北京市朝阳区</p>
                            </div>
                        </div>

                        <div class="social-links">
                            <a href="#" class="social-link" aria-label="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="LinkedIn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="微博">
                                <i class="fab fa-weibo"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="微信">
                                <i class="fab fa-weixin"></i>
                            </a>
                        </div>
                    </div>

                    <div class="contact-form">
                        <form class="form" id="contact-form">
                            <div class="form-group">
                                <label for="name">姓名</label>
                                <input type="text" id="name" name="name" required>
                            </div>

                            <div class="form-group">
                                <label for="email">邮箱</label>
                                <input type="email" id="email" name="email" required>
                            </div>

                            <div class="form-group">
                                <label for="subject">主题</label>
                                <input type="text" id="subject" name="subject" required>
                            </div>

                            <div class="form-group">
                                <label for="message">消息</label>
                                <textarea id="message" name="message" rows="5" required></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                发送消息
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2025 张三. 保留所有权利.</p>
                    <p>使用现代Web技术精心制作</p>
                </div>

                <div class="footer-links">
                    <a href="#home">首页</a>
                    <a href="#about">关于</a>
                    <a href="#skills">技能</a>
                    <a href="#projects">项目</a>
                    <a href="#contact">联系</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="back-to-top" aria-label="返回顶部">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- JavaScript文件 -->
    <script src="js/main.js"></script>
</body>
</html>
