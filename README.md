# 现代化个人主页

一个采用2025年流行设计风格的现代化个人主页网站，使用纯PHP开发，具有响应式设计和深色模式支持。

## 🌟 特性

- **现代化设计**: 采用2025年流行的UI设计风格
- **响应式布局**: 完美适配桌面端和移动端
- **深色模式**: 支持明暗主题切换
- **毛玻璃效果**: 现代化的视觉效果
- **渐变色彩**: 精美的渐变色彩搭配
- **平滑动画**: 流畅的交互动画效果
- **SEO优化**: 良好的搜索引擎优化

## 🛠️ 技术栈

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **后端**: PHP (纯前端展示，无后端逻辑)
- **图标**: Font Awesome 6.5.0
- **字体**: 系统默认字体栈

## 📁 项目结构

```
├── index.php          # 主页面文件
├── styles/
│   └── main.css       # 主样式文件
├── js/
│   └── main.js        # 主JavaScript文件
├── images/            # 图片资源目录
└── README.md          # 项目说明文档
```

## 🚀 快速开始

### 环境要求

- PHP 7.4 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装和运行

1. **克隆或下载项目**
   ```bash
   # 如果是Git仓库
   git clone <repository-url>
   cd personal-homepage
   ```

2. **启动PHP开发服务器**
   ```bash
   php -S localhost:8000
   ```

3. **在浏览器中访问**
   ```
   http://localhost:8000
   ```

## 🎨 设计特色

### 颜色主题
- **主色调**: 紫蓝色渐变 (#667eea → #764ba2)
- **辅助色**: 粉红色渐变 (#f093fb → #f5576c)
- **强调色**: 蓝青色渐变 (#4facfe → #00f2fe)

### 视觉效果
- **毛玻璃效果**: backdrop-filter: blur(10px)
- **阴影系统**: 多层次阴影设计
- **圆角设计**: 现代化的圆角处理
- **动画效果**: 平滑的过渡和交互动画

## 📱 响应式设计

- **桌面端**: 1200px+ 完整布局
- **平板端**: 768px-1024px 适配布局
- **移动端**: 320px-768px 移动优化

## 🌙 深色模式

支持系统级深色模式检测和手动切换：
- 自动保存用户偏好设置
- 平滑的主题切换动画
- 完整的深色模式适配

## 🔧 自定义配置

### 修改个人信息

编辑 `index.php` 文件中的以下内容：

```php
// 基本信息
$name = "张三";
$title = "全栈开发工程师";
$email = "<EMAIL>";
$phone = "+86 138 0000 0000";
$location = "北京市朝阳区";
```

### 修改技能信息

在 `index.php` 中找到技能展示部分，修改技能名称和进度：

```html
<div class="skill-progress" data-progress="95"></div>
```

### 修改项目信息

在项目展示部分添加或修改项目信息：

```html
<div class="project-card">
    <!-- 项目内容 -->
</div>
```

## 🎯 功能说明

### 导航功能
- 固定顶部导航栏
- 平滑滚动到对应区域
- 移动端汉堡菜单
- 当前区域高亮显示

### 交互功能
- 深色模式切换
- 返回顶部按钮
- 联系表单验证
- 滚动动画效果

### 动画效果
- 页面加载动画
- 滚动触发动画
- 技能进度条动画
- 悬停交互效果

## 🔍 SEO优化

- 语义化HTML结构
- Meta标签优化
- 图片Alt属性
- 结构化数据
- 页面性能优化

## 🌐 浏览器兼容性

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: <EMAIL>
- GitHub: [您的GitHub用户名]
- 微信: [您的微信号]

---

**注意**: 这是一个纯前端展示项目，联系表单仅做演示用途，如需实际发送邮件功能，请添加相应的后端处理逻辑。
