/**
 * 个人主页样式文件
 * 采用2025年流行的现代化设计风格
 * 支持深色模式和响应式设计
 */

/* CSS自定义属性 - 颜色主题 */
:root {
    /* 主色调 */
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    
    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 语义色 */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* 毛玻璃效果 */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-backdrop: blur(10px);
    
    /* 动画 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
}

/* 深色模式主题 */
[data-theme="dark"] {
    --white: #111827;
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-400: #9ca3af;
    --gray-500: #d1d5db;
    --gray-600: #e5e7eb;
    --gray-700: #f3f4f6;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
    
    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-900);
    background-color: var(--white);
    overflow-x: hidden;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border-bottom: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-md);
}

[data-theme="dark"] .navbar.scrolled {
    background: rgba(17, 24, 39, 0.95);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--gray-700);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.hamburger {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    gap: 4px;
}

.hamburger span {
    width: 24px;
    height: 2px;
    background: var(--gray-700);
    transition: all var(--transition-fast);
}

/* 英雄区域 */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.1;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.hero-text {
    z-index: 1;
}

.greeting {
    display: block;
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: var(--spacing-sm);
}

.name {
    display: block;
    font-size: 3.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--gray-700);
    margin-bottom: var(--spacing-lg);
}

.hero-description {
    font-size: 1.125rem;
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: var(--spacing-xl);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 2px solid var(--gray-200);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* 头像区域 */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 3D头像容器 */
.avatar-container {
    position: relative;
    width: 300px;
    height: 300px;
    perspective: 1000px;
    transform-style: preserve-3d;
}

/* 3D头像主体 */
.avatar {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 6rem;
    color: white;
    position: relative;
    z-index: 2;
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    cursor: pointer;

    /* 3D阴影效果 */
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 10px 20px rgba(0, 0, 0, 0.1),
        0 5px 10px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);

    /* 3D质感滤镜 */
    filter:
        brightness(1.05)
        contrast(1.1)
        saturate(1.2)
        drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

/* 3D光照效果 */
.avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(0, 0, 0, 0.1) 100%);
    border-radius: var(--radius-xl);
    transition: opacity 0.3s ease;
    pointer-events: none;
}

/* 3D反光效果 */
.avatar::after {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 30%;
    height: 30%;
    background: radial-gradient(ellipse at center,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 70%);
    border-radius: 50%;
    transform: translateZ(1px);
    transition: all 0.3s ease;
    pointer-events: none;
}

/* 3D悬停效果 */
.avatar:hover {
    transform: translateZ(20px) rotateX(-10deg) rotateY(10deg) scale(1.05);
    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.25),
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 10px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.avatar:hover::before {
    opacity: 0.8;
}

.avatar:hover::after {
    opacity: 1;
    transform: translateZ(2px) scale(1.2);
}

/* 3D环形装饰 */
.avatar-ring {
    position: absolute;
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    border: 4px solid transparent;
    border-radius: calc(var(--radius-xl) + 30px);
    background: var(--gradient-accent);
    background-clip: padding-box;
    transform-style: preserve-3d;
    animation: rotate3d 15s linear infinite;
    opacity: 0.7;
    z-index: 1;

    /* 3D环形阴影 */
    box-shadow:
        0 0 20px rgba(79, 172, 254, 0.3),
        0 0 40px rgba(79, 172, 254, 0.2),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

/* 3D环形内层 */
.avatar-ring::before {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    right: 4px;
    bottom: 4px;
    border-radius: calc(var(--radius-xl) + 26px);
    background: var(--white);
    z-index: -1;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 3D环形外层光晕 */
.avatar-ring::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: calc(var(--radius-xl) + 40px);
    background: radial-gradient(circle,
        rgba(79, 172, 254, 0.2) 0%,
        rgba(79, 172, 254, 0.1) 50%,
        transparent 70%);
    z-index: -2;
    animation: pulse3d 3s ease-in-out infinite;
}

/* 3D粒子效果 */
.avatar-container::before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    right: -50px;
    bottom: -50px;
    background-image:
        radial-gradient(2px 2px at 20% 30%, rgba(255, 255, 255, 0.8), transparent),
        radial-gradient(2px 2px at 40% 70%, rgba(79, 172, 254, 0.6), transparent),
        radial-gradient(1px 1px at 90% 40%, rgba(255, 119, 198, 0.8), transparent),
        radial-gradient(1px 1px at 60% 10%, rgba(255, 255, 255, 0.6), transparent),
        radial-gradient(2px 2px at 80% 80%, rgba(79, 172, 254, 0.4), transparent);
    background-size: 100px 100px, 80px 80px, 60px 60px, 40px 40px, 120px 120px;
    animation: float3d 20s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
}

/* 3D动画关键帧 */
@keyframes rotate3d {
    0% { transform: rotateZ(0deg) rotateY(0deg); }
    25% { transform: rotateZ(90deg) rotateY(90deg); }
    50% { transform: rotateZ(180deg) rotateY(180deg); }
    75% { transform: rotateZ(270deg) rotateY(270deg); }
    100% { transform: rotateZ(360deg) rotateY(360deg); }
}

@keyframes pulse3d {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1) translateZ(0);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1) translateZ(5px);
    }
}

@keyframes float3d {
    0%, 100% {
        transform: translateY(0px) rotateZ(0deg);
        opacity: 0.7;
    }
    33% {
        transform: translateY(-20px) rotateZ(120deg);
        opacity: 1;
    }
    66% {
        transform: translateY(20px) rotateZ(240deg);
        opacity: 0.8;
    }
}

/* 呼吸灯效果 */
@keyframes breathe {
    0%, 100% {
        filter: brightness(1) saturate(1);
        transform: scale(1);
    }
    50% {
        filter: brightness(1.1) saturate(1.2);
        transform: scale(1.02);
    }
}

/* 滚动指示器 */
.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    color: var(--gray-500);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* 通用区域样式 */
section {
    padding: var(--spacing-3xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* 关于我区域 */
.about {
    background: var(--gray-50);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.about-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-xl);
}

.about-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.about-card p {
    color: var(--gray-600);
    line-height: 1.7;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
}

.about-features {
    display: grid;
    gap: var(--spacing-lg);
}

.feature-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: transform var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: var(--spacing-lg);
}

.feature-card h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 技能区域 */
.skills {
    background: var(--white);
}

.skills-content {
    display: grid;
    gap: var(--spacing-3xl);
}

.skill-category h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.skill-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.skill-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.skill-item:hover {
    background: var(--white);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.skill-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.skill-info {
    flex: 1;
}

.skill-name {
    display: block;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.skill-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width 1s ease-in-out;
    width: 0;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    justify-content: center;
}

.skill-tag {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    transition: transform var(--transition-fast);
}

.skill-tag:hover {
    transform: scale(1.05);
}

/* 项目展示区域 */
.projects {
    background: var(--gray-50);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.project-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-placeholder {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: var(--spacing-lg);
}

.project-link {
    width: 50px;
    height: 50px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-700);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.project-link:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.project-content {
    padding: var(--spacing-xl);
}

.project-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.project-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tech-tag {
    padding: var(--spacing-xs) var(--spacing-md);
    background: var(--gray-100);
    color: var(--gray-700);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

/* 联系方式区域 */
.contact {
    background: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
}

.contact-info {
    display: grid;
    gap: var(--spacing-lg);
}

.contact-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.contact-card:hover {
    background: var(--white);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
}

.contact-details p {
    color: var(--gray-600);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    font-size: 1.25rem;
    transition: all var(--transition-normal);
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-lg);
}

/* 表单样式 */
.contact-form {
    background: var(--gray-50);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
}

.form {
    display: grid;
    gap: var(--spacing-lg);
}

.form-group {
    display: grid;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: 600;
    color: var(--gray-700);
}

.form-group input,
.form-group textarea {
    padding: var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
    background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* 页脚 */
.footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: var(--spacing-xl) 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.footer-text p {
    margin-bottom: var(--spacing-xs);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-light);
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

@media (max-width: 768px) {
    .name {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .skill-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    /* 移动端3D头像优化 */
    .avatar-container {
        width: 200px;
        height: 200px;
        perspective: 800px; /* 减少透视距离适应小屏幕 */
    }

    .avatar {
        font-size: 4rem;
    }

    /* 移动端减少3D效果强度 */
    .avatar:hover {
        transform: translateZ(15px) rotateX(-5deg) rotateY(5deg) scale(1.03);
    }

    /* 移动端3D环形调整 */
    .avatar-ring {
        top: -20px;
        left: -20px;
        right: -20px;
        bottom: -20px;
        border-width: 3px;
    }

    /* 移动端粒子效果优化 */
    .avatar-container::before {
        top: -30px;
        left: -30px;
        right: -30px;
        bottom: -30px;
        background-size: 60px 60px, 50px 50px, 40px 40px, 30px 30px, 80px 80px;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .nav-container {
        padding: var(--spacing-md) var(--spacing-sm);
    }

    .name {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .skill-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-card {
        flex-direction: column;
        text-align: center;
    }

    .social-links {
        flex-wrap: wrap;
    }
}

/* 动画和过渡效果 */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 深色模式特定样式 */
[data-theme="dark"] .navbar.scrolled {
    background: rgba(17, 24, 39, 0.95);
}

[data-theme="dark"] .project-placeholder {
    background: var(--gradient-secondary);
}

[data-theme="dark"] .skill-item {
    background: var(--gray-100);
}

[data-theme="dark"] .skill-item:hover {
    background: var(--gray-50);
}

[data-theme="dark"] .contact-card {
    background: var(--gray-100);
}

[data-theme="dark"] .contact-card:hover {
    background: var(--gray-50);
}

[data-theme="dark"] .contact-form {
    background: var(--gray-100);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea {
    background: var(--gray-50);
    border-color: var(--gray-300);
    color: var(--gray-900);
}

/* 打印样式 */
@media print {
    .navbar,
    .back-to-top,
    .hero-particles,
    .scroll-indicator {
        display: none;
    }

    .hero {
        min-height: auto;
        padding: var(--spacing-xl) 0;
    }

    section {
        padding: var(--spacing-lg) 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
    }

    * {
        box-shadow: none !important;
    }
}
